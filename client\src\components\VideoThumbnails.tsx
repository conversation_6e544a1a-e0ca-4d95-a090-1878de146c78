import { useState, useEffect } from "react";
import {
	Box,
	Group,
	Image,
	Text,
	Skeleton,
	Paper,
	ScrollArea,
	Badge,
	Overlay,
	ActionIcon,
} from "@mantine/core";
import { IconCheck, IconPlayerPlay } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import apiClient from "../config/axios";
import type { videoDataType } from "../types";

interface VideoThumbnailsProps {
	videoType: videoDataType;
	onThumbnailClick?: (thumbnailUrl: string) => void;
	showSelection?: boolean;
	onThumbnailSelected?: () => void;
}

interface ThumbnailData {
	thumbnailUrls: string[];
	thumbnailKeys: string[];
	thumbnailStatus: "pending" | "processing" | "completed" | "failed";
	thumbnailCount: number;
	selectedThumbnailUrl: string | null;
	selectedThumbnailKey: string | null;
}

const VideoThumbnails = ({
	videoType,
	onThumbnailClick,
	showSelection = false,
	onThumbnailSelected,
}: VideoThumbnailsProps) => {
	const [thumbnailData, setThumbnailData] = useState<ThumbnailData | null>(
		null
	);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selecting, setSelecting] = useState(false);

	const fetchThumbnails = async () => {
		try {
			setLoading(true);
			setError(null);

			const response = await apiClient.get("/api/videos/thumbnails", {
				params: { videoType },
			});

			setThumbnailData(response.data);
		} catch (err: unknown) {
			console.error("Error fetching thumbnails:", err);
			const errorMessage =
				err && typeof err === "object" && "response" in err
					? (err as any).response?.data?.message ||
						"Failed to load thumbnails"
					: "Failed to load thumbnails";
			setError(errorMessage);
		} finally {
			setLoading(false);
		}
	};

	const handleThumbnailSelect = async (
		_thumbnailUrl: string,
		index: number
	) => {
		if (!thumbnailData || !showSelection) return;

		try {
			setSelecting(true);

			// Use the actual thumbnail key from the backend
			const thumbnailKey = thumbnailData.thumbnailKeys[index];

			if (!thumbnailKey) {
				throw new Error("Could not determine thumbnail key");
			}

			await apiClient.post("/api/videos/thumbnails/select", {
				videoType,
				thumbnailKey: thumbnailKey,
			});

			// Refresh thumbnail data to get updated selection
			await fetchThumbnails();

			// Call the callback to notify parent component
			onThumbnailSelected?.();

			notifications.show({
				title: "Thumbnail Selected",
				message: "Your thumbnail selection has been saved.",
				color: "green",
			});
		} catch (err: unknown) {
			console.error("Error selecting thumbnail:", err);
			const errorMessage =
				err && typeof err === "object" && "response" in err
					? (err as any).response?.data?.message ||
						"Failed to select thumbnail"
					: "Failed to select thumbnail";
			notifications.show({
				title: "Selection Failed",
				message: errorMessage,
				color: "red",
			});
		} finally {
			setSelecting(false);
		}
	};

	useEffect(() => {
		fetchThumbnails();
	}, [videoType]); // eslint-disable-line react-hooks/exhaustive-deps

	if (loading) {
		return (
			<Box>
				<Text size="sm" c="dimmed" mb="md">
					Video Thumbnails
				</Text>
				<Group gap="md">
					{Array.from({ length: 5 }).map((_, index) => (
						<Skeleton
							key={index}
							height={120}
							width={160}
							radius="md"
						/>
					))}
				</Group>
			</Box>
		);
	}

	if (error) {
		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Text size="sm" c="red">
					{error}
				</Text>
			</Box>
		);
	}

	if (!thumbnailData || thumbnailData.thumbnailUrls.length === 0) {
		const statusMessage =
			thumbnailData?.thumbnailStatus === "processing"
				? "Generating thumbnails..."
				: thumbnailData?.thumbnailStatus === "failed"
					? "Failed to generate thumbnails"
					: "No thumbnails available";

		return (
			<Box>
				<Text size="sm" c="dimmed" mb="xs">
					Video Thumbnails
				</Text>
				<Text size="sm" c="dimmed">
					{statusMessage}
				</Text>
			</Box>
		);
	}

	return (
		<Box>
			<Text size="sm" c="dimmed" mb="md">
				Video Thumbnails ({thumbnailData.thumbnailCount})
				{showSelection && thumbnailData.selectedThumbnailUrl && (
					<Badge size="xs" color="green" ml="xs" variant="light">
						Selected
					</Badge>
				)}
			</Text>
			<ScrollArea>
				<Group gap="md" wrap="nowrap">
					{thumbnailData.thumbnailUrls.map((thumbnailUrl, index) => {
						const isSelected =
							showSelection &&
							thumbnailData.selectedThumbnailUrl === thumbnailUrl;
						const canClick = onThumbnailClick || showSelection;

						return (
							<Box
								key={index}
								style={{
									position: "relative",
									minWidth: 160,
								}}
							>
								<Paper
									shadow={isSelected ? "lg" : "sm"}
									radius="md"
									style={{
										cursor: canClick ? "pointer" : "default",
										transition: "all 0.3s ease",
										border: isSelected
											? "3px solid var(--mantine-color-blue-6)"
											: "3px solid transparent",
										opacity: selecting ? 0.7 : 1,
										overflow: "hidden",
										position: "relative",
									}}
									onMouseEnter={e => {
										if (canClick && !selecting) {
											e.currentTarget.style.transform =
												"scale(1.02)";
											e.currentTarget.style.boxShadow =
												"var(--mantine-shadow-md)";
										}
									}}
									onMouseLeave={e => {
										if (canClick && !selecting) {
											e.currentTarget.style.transform =
												"scale(1)";
											e.currentTarget.style.boxShadow = isSelected
												? "var(--mantine-shadow-lg)"
												: "var(--mantine-shadow-sm)";
										}
									}}
									onClick={() => {
										if (selecting) return;
										if (showSelection) {
											handleThumbnailSelect(
												thumbnailUrl,
												index
											);
										} else if (onThumbnailClick) {
											onThumbnailClick(thumbnailUrl);
										}
									}}
								>
									<Image
										src={thumbnailUrl}
										alt={`Thumbnail ${index + 1}`}
										height={120}
										width={160}
										fit="cover"
										radius="md"
										fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDE2MCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03MCA1MEw5MCA2MEw3MCA3MFY1MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+"
									/>

									{/* Play icon overlay for non-selected thumbnails */}
									{!isSelected && canClick && (
										<Overlay
											opacity={0}
											style={{
												transition: "opacity 0.2s ease",
												display: "flex",
												alignItems: "center",
												justifyContent: "center",
											}}
											onMouseEnter={e => {
												if (!selecting) {
													e.currentTarget.style.opacity = "0.8";
												}
											}}
											onMouseLeave={e => {
												e.currentTarget.style.opacity = "0";
											}}
										>
											<ActionIcon
												size="xl"
												radius="xl"
												variant="filled"
												color="blue"
												style={{
													backgroundColor: "rgba(37, 99, 235, 0.9)",
												}}
											>
												<IconPlayerPlay size="1.5rem" />
											</ActionIcon>
										</Overlay>
									)}

									{/* Selected indicator */}
									{isSelected && (
										<Box
											style={{
												position: "absolute",
												top: 8,
												right: 8,
												backgroundColor: "var(--mantine-color-blue-6)",
												borderRadius: "50%",
												width: 32,
												height: 32,
												display: "flex",
												alignItems: "center",
												justifyContent: "center",
												boxShadow: "0 2px 8px rgba(0,0,0,0.2)",
											}}
										>
											<IconCheck size="1.2rem" color="white" />
										</Box>
									)}
								</Paper>

								{/* Thumbnail number indicator */}
								<Text
									size="xs"
									c="dimmed"
									ta="center"
									mt="xs"
									style={{ userSelect: "none" }}
								>
									{index + 1}
								</Text>
							</Box>
						);
					})}
				</Group>
			</ScrollArea>
		</Box>
	);
};

export default VideoThumbnails;
