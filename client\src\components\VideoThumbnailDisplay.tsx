import { useState, useEffect } from "react";
import { Box, Image, Text, Skeleton } from "@mantine/core";
import apiClient from "../config/axios";
import type { videoDataType } from "../types";

interface VideoThumbnailDisplayProps {
	videoType: videoDataType;
	width?: number;
	height?: number;
}

interface ThumbnailData {
	selectedThumbnailUrl: string | null;
	thumbnailStatus: "pending" | "processing" | "completed" | "failed";
}

const VideoThumbnailDisplay = ({
	videoType,
	width = 120,
	height = 90,
}: VideoThumbnailDisplayProps) => {
	const [thumbnailData, setThumbnailData] = useState<ThumbnailData | null>(
		null
	);
	const [loading, setLoading] = useState(true);

	const fetchSelectedThumbnail = async () => {
		try {
			setLoading(true);

			const response = await apiClient.get("/api/videos/thumbnails", {
				params: { videoType },
			});

			setThumbnailData({
				selectedThumbnailUrl: response.data.selectedThumbnailUrl,
				thumbnailStatus: response.data.thumbnailStatus,
			});
		} catch (err) {
			console.error("Error fetching selected thumbnail:", err);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchSelectedThumbnail();
	}, [videoType]); // eslint-disable-line react-hooks/exhaustive-deps

	if (loading) {
		return <Skeleton height={height} width={width} radius="sm" />;
	}

	if (!thumbnailData?.selectedThumbnailUrl) {
		return (
			<Box
				style={{
					width,
					height,
					backgroundColor: "#f5f5f5",
					borderRadius: "var(--mantine-radius-sm)",
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
			>
				<Text size="xs" c="dimmed" ta="center">
					No thumbnail selected
				</Text>
			</Box>
		);
	}

	return (
		<Image
			src={thumbnailData.selectedThumbnailUrl}
			alt="Selected video thumbnail"
			width={width}
			height={height}
			fit="cover"
			radius="sm"
			fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjkwIiB2aWV3Qm94PSIwIDAgMTIwIDkwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTIwIiBoZWlnaHQ9IjkwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik01MCAzN0w3MCA0NUw1MCA1M1YzN1oiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+"
		/>
	);
};

export default VideoThumbnailDisplay;
