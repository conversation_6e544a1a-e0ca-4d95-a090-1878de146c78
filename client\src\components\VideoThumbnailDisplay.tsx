import { useState, useEffect } from "react";
import { Image, Text, Skeleton, Paper } from "@mantine/core";
import apiClient from "../config/axios";
import type { videoDataType } from "../types";

interface VideoThumbnailDisplayProps {
	videoType: videoDataType;
	width?: number;
	height?: number;
}

interface ThumbnailData {
	selectedThumbnailUrl: string | null;
	thumbnailStatus: "pending" | "processing" | "completed" | "failed";
}

const VideoThumbnailDisplay = ({
	videoType,
	width = 200,
	height = 150,
}: VideoThumbnailDisplayProps) => {
	const [thumbnailData, setThumbnailData] = useState<ThumbnailData | null>(
		null
	);
	const [loading, setLoading] = useState(true);

	const fetchSelectedThumbnail = async () => {
		try {
			setLoading(true);

			const response = await apiClient.get("/api/videos/thumbnails", {
				params: { videoType },
			});

			setThumbnailData({
				selectedThumbnailUrl: response.data.selectedThumbnailUrl,
				thumbnailStatus: response.data.thumbnailStatus,
			});
		} catch (err) {
			console.error("Error fetching selected thumbnail:", err);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchSelectedThumbnail();
	}, [videoType]); // eslint-disable-line react-hooks/exhaustive-deps

	if (loading) {
		return <Skeleton height={height} width={width} radius="md" />;
	}

	if (!thumbnailData?.selectedThumbnailUrl) {
		return (
			<Paper
				shadow="sm"
				radius="md"
				style={{
					width,
					height,
					backgroundColor: "#f8f9fa",
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
					border: "2px dashed #dee2e6",
				}}
			>
				<Text size="sm" c="dimmed" ta="center">
					No thumbnail selected
				</Text>
			</Paper>
		);
	}

	return (
		<Paper shadow="md" radius="md" style={{ overflow: "hidden" }}>
			<Image
				src={thumbnailData.selectedThumbnailUrl}
				alt="Selected video thumbnail"
				width={width}
				height={height}
				fit="cover"
				radius="md"
				fallbackSrc="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik04MCA2MEwxMjAgNzVMODAgOTBWNjBaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPg=="
			/>
		</Paper>
	);
};

export default VideoThumbnailDisplay;
