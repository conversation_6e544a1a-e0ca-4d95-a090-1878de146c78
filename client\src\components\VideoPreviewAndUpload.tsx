import { Group, Box, Title, Stack, Text, Card } from "@mantine/core";
import { useState, useEffect, useCallback } from "react";
import apiClient from "../config/axios";

import { videoTypeLabel } from "../constants";
import type { videoDataType } from "../types";
import VideoThumbnails from "./VideoThumbnails";
import VideoThumbnailDisplay from "./VideoThumbnailDisplay";

interface VideoPreviewAndUploadProps {
	videoPreviewUrl: string | null;
	videoType: videoDataType;
	setHasUnsavedChanges?: (hasUnsavedChanges: boolean) => void;
	editing?: boolean;
}

//remove unused code for upload

const VideoPreviewAndUpload = ({
	videoPreviewUrl,
	videoType,
}: VideoPreviewAndUploadProps) => {
	const [selectedThumbnailUrl, setSelectedThumbnailUrl] = useState<
		string | null
	>(null);

	const fetchSelectedThumbnail = useCallback(async () => {
		try {
			const response = await apiClient.get("/api/videos/thumbnails", {
				params: { videoType },
			});
			setSelectedThumbnailUrl(response.data.selectedThumbnailUrl);
		} catch (err) {
			console.error("Error fetching selected thumbnail:", err);
		}
	}, [videoType]);

	useEffect(() => {
		if (videoPreviewUrl) {
			fetchSelectedThumbnail();
		}
	}, [videoType, videoPreviewUrl, fetchSelectedThumbnail]);

	return (
		<Group justify="space-between" align="center" mb={32} wrap="nowrap">
			<Card
				shadow="sm"
				radius="lg"
				withBorder
				h={"28rem"}
				w={"48%"}
				p={"lg"}
			>
				<Stack h="100%" justify="center">
					<Stack gap={1}>
						<Title order={2}>
							{videoTypeLabel[videoType]} Video
						</Title>
						<Text c="gray">Watch the uploaded video.</Text>
					</Stack>
					<Group gap="md" align="flex-start">
						<Box style={{ flex: 1 }}>
							<Text size="sm" c="dimmed" mb="xs">
								Video Player
							</Text>
							<Box
								style={{
									backgroundColor: "black",
									borderRadius: "var(--mantine-radius-md)",
									overflow: "hidden",
									height: "300px",
									width: "100%",
								}}
							>
								<video
									controls
									src={videoPreviewUrl ? videoPreviewUrl : ""}
									poster={selectedThumbnailUrl || undefined}
									style={{
										width: "100%",
										height: "100%",
										objectFit: "contain",
									}}
								/>
							</Box>
						</Box>
						{videoPreviewUrl && (
							<Box>
								<Text size="sm" c="dimmed" mb="xs">
									Selected Thumbnail
								</Text>
								<VideoThumbnailDisplay videoType={videoType} />
							</Box>
						)}
					</Group>
					{videoPreviewUrl && (
						<Box mt="md">
							<VideoThumbnails
								videoType={videoType}
								showSelection={true}
								onThumbnailSelected={fetchSelectedThumbnail}
							/>
						</Box>
					)}
				</Stack>
			</Card>
		</Group>
	);
};

export default VideoPreviewAndUpload;
